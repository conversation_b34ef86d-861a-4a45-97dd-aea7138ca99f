<!-- view.vue -->
<template>
  <view class="container">
    <!-- 背景效果元素 -->
    <view class="gradient-blob blob-1"></view>
    <view class="gradient-blob blob-2"></view>
    <view class="gradient-blob blob-3"></view>
    <view class="decorative-pattern"></view>
    
    <view class="content">
      <view class="header">
        <text class="title">{{ searchName ? `${searchName} 的留言箱` : '悄悄话留言箱' }}</text>
        <text class="subtitle">输入一个名字，打开一叠无人知晓的独白...</text>
      </view>
      
      <view class="search-bar">
        <view class="search-input-wrapper">
          <input 
            class="search-input" 
            placeholder="输入名字查看留言" 
            v-model="searchName" 
            @focus="onInputFocus" 
            @blur="onInputBlur" 
          />
        </view>
        <button class="search-button" @tap="viewMessages">
          <view class="search-icon"></view>
          <text class="button-text">查看</text>
        </button>
      </view>
      
      <view class="message-list">
        <!-- 有留言时的展示 -->
        <block v-if="messages.length > 0">
          <!-- 还原回原来的气泡样式 -->
          <view class="message-bubble">
            <text class="bubble-text">{{searchName}} 收到了 {{totalMessages}} 条留言 ✨</text>
            <button class="invite-mini-btn" open-type="share">邀请留言</button>
          </view>

          <!-- 留言列表 -->
          <view 
            class="message-item" 
            v-for="(message, index) in messages" 
            :key="index" 
            :class="{ 'message-appear': showAnimation }" 
            :style="{ animationDelay: `${index * 0.1}s` }"
          >
            <view class="message-header">
              <view class="user-avatar-wrapper">
                <image class="user-avatar" :src="formatAvatarUrl(message.author_info ? message.author_info.avatarUrl : '')" mode="aspectFill"></image>
                <text class="message-user">{{ message.author_info ? message.author_info.nickname : '留言用户' }}</text>
              </view>
              <view class="message-meta">
                <!-- 互换位置：先显示时间，再显示点赞按钮 -->
                <text class="message-time">{{ formatTime(message.created_at) }}</text>
                <view 
                  class="message-like-btn" 
                  @tap.stop="toggleLikeMessage(message)"
                  :class="{ 'liked': message.is_liked_by_user }"
                >
                  <view class="like-icon"></view>
                  <text class="like-count">{{message.like_count || 0}}</text>
                </view>
              </view>
            </view>
            <view class="message-content-wrapper">
              <text class="message-content">{{ message.content }}</text>
            </view>
          </view>

          <!-- 骨架屏加载提示 -->
          <view v-if="showSkeleton" class="skeleton-container">
            <view class="skeleton-item" v-for="i in 2" :key="i">
              <view class="skeleton-header">
                <view class="skeleton-user skeleton-shimmer-base"></view>
                <view class="skeleton-time skeleton-shimmer-base"></view>
              </view>
              <view class="skeleton-content skeleton-shimmer-base"></view>
              <view class="skeleton-content short skeleton-shimmer-base"></view>
            </view>
          </view>

          <!-- 底部提示 -->
          <view class="bottom-hint">
            <text class="hint-link" @tap="goToWrite">写一条留言给 {{searchName}} ></text>
          </view>
        </block>

        <!-- 无留言时的展示 -->
        <block v-else>
          <!-- 骨架屏加载提示 -->
          <view v-if="showSkeleton" class="skeleton-container">
            <view class="skeleton-item" v-for="i in 2" :key="i">
              <view class="skeleton-header">
                <view class="skeleton-user skeleton-shimmer-base"></view>
                <view class="skeleton-time skeleton-shimmer-base"></view>
              </view>
              <view class="skeleton-content skeleton-shimmer-base"></view>
              <view class="skeleton-content short skeleton-shimmer-base"></view>
            </view>
          </view>
          
          <!-- 引导界面 -->
          <view v-else class="no-data-guide">
            <view class="guide-icon">
              <image src="/static/write-icon.gif" mode="aspectFit" class="icon"></image>
            </view>
            <view class="guide-text">
              还没有人给 <text class="highlight">{{searchName}}</text> 写留言
            </view>
            <view class="guide-subtext">
              但你可以写第一封！快邀请好友来写吧~
            </view>
            <view class="button-group">
              <button class="write-button" @tap="goToWrite">
                <text class="button-text">写第一封信/留言</text>
              </button>
              <button class="invite-button" open-type="share">
                <text class="button-text">邀请好友写信</text>
              </button>
            </view>
          </view>
          
          <!-- 推荐留言区域 -->
          <view class="recommended-messages" v-if="showRecommendations && recommendations.length > 0">
            <view class="section-header">
              <text class="section-title">💌 {{recommendationTitle}}</text>
              <text class="section-subtitle">" 这些字句，是否也飘向你认识的那个TA？ "</text>
            </view>

            <view
              class="message-item recommended-item"
              v-for="(message, index) in recommendations"
              :key="index"
              :class="{ 'message-appear': showAnimation }"
              :style="{ animationDelay: `${index * 0.1}s` }"
            >
              <!-- 收信人信息 -->
              <view class="message-header">
                <view class="recipient-wrapper">
                  <image class="user-avatar" :src="formatAvatarUrl(message.author_info ? message.author_info.avatarUrl : '')" mode="aspectFill"></image>
                  <text class="recipient-prefix">给</text>
                  <text class="recipient-name">{{formatName(message.name)}}</text>
                  <text class="recipient-suffix">的一封信</text>
                </view>
                <view class="message-meta">
                  <!-- 互换位置：先显示时间，再显示点赞按钮 -->
                  <text class="message-time">{{formatTime(message.created_at)}}</text>
                  <view 
                    class="message-like-btn" 
                    @tap.stop="toggleLikeMessage(message)"
                    :class="{ 'liked': message.is_liked_by_user }"
                  >
                    <view class="like-icon"></view>
                    <text class="like-count">{{message.like_count || 0}}</text>
                  </view>
                </view>
              </view>

              <!-- 信件内容预览 -->
              <view class="message-content-wrapper">
                <text class="message-content">{{message.content}}</text>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      messages: [],
      searchName: '',
      isButtonHovered: false,
      isInputFocused: false,
      hasMoreMessages: false,
      recommendedMessages: [],
      token: null,
      currentPage: 1,
      pageSize: 30,
      totalMessages: 0,
      isLoading: false,
      // 骨架屏相关
      showSkeleton: false,
      // 添加动画控制状态
      showAnimation: false,
      // 分享配置
      shareConfig: {
        title: '悄悄话留言箱',
        imageUrl: '/static/share-icon.png'
      },
      // 点赞状态
      isLikeLoading: false,
      
      // 点赞频率限制
      likeActionTimestamps: [], // 记录点赞操作时间戳
      RATE_LIMIT_WINDOW: 10000, // 时间窗口：10秒 (毫秒)
      RATE_LIMIT_COUNT: 5,      // 窗口内允许的最大次数
      
      // 新增：首字符，用于显示"同样姓x的名字有这些留言"
      firstChar: '',

      // 推荐留言相关
      recommendations: [],
      showRecommendations: false,
      recommendationTitle: '',
    };
  },

  onLoad(options) {
    const savedToken = uni.getStorageSync('mailbox_token');
    if (savedToken) {
      this.token = savedToken;
    }
    
    if (options.name) {
      this.searchName = decodeURIComponent(options.name);
      this.initializeAndFetchMessages(this.searchName);
    } else {
      this.initializeAndFetchMessages();
    }
    
    // 获取分享配置
    this.fetchShareConfig('view');
  },
  
  // 添加触底加载更多的生命周期函数
  onReachBottom() {
    if (this.hasMoreMessages && !this.isLoading) {
      this.loadMoreMessages();
    }
  },
  
  // 分享给好友的生命周期函数
  onShareAppMessage() {
    const name = this.searchName || '神秘人';  // 如果没有名字则显示"神秘人"
    
    const formattedTitle = this.formatShareTitle(this.shareConfig.title, name);
    
    // --- 拼接完整的图片 URL ---
    let fullImageUrl = this.shareConfig.imageUrl;
    if (fullImageUrl && fullImageUrl.startsWith('/') && this.$baseUrl) {
      fullImageUrl = this.$baseUrl + fullImageUrl;
    }
    // --- URL 拼接结束 ---
    
    const shareInfo = {
      title: formattedTitle,
      path: `/pages/view/view?name=${encodeURIComponent(this.searchName)}`,
      imageUrl: fullImageUrl, // 使用处理后的 URL
      success: () => {
        uni.showToast({
          title: '分享成功',
          icon: 'success',
          duration: 1500
        });
      },
      fail: (error) => {
        console.error('分享失败:', error); // 保留错误日志
        uni.showToast({
          title: '分享失败',
          icon: 'none'
        });
      }
    };
    
    return shareInfo;
  },
  
  // 分享到朋友圈的生命周期函数
  onShareTimeline() {
    const name = this.searchName || '神秘人';  // 保持两种分享方式的标题一致
    
    const formattedTitle = this.formatShareTitle(this.shareConfig.title, name);
    
    // --- 拼接完整的图片 URL ---
    let fullImageUrl = this.shareConfig.imageUrl;
    if (fullImageUrl && fullImageUrl.startsWith('/') && this.$baseUrl) {
      fullImageUrl = this.$baseUrl + fullImageUrl;
    }
    // --- URL 拼接结束 ---
    
    const shareInfo = {
      title: formattedTitle,
      query: `name=${encodeURIComponent(this.searchName)}`,
      imageUrl: fullImageUrl // 使用处理后的 URL
    };
    
    return shareInfo;
  },

  methods: {
    async initializeAndFetchMessages(name = '') {
      await this.ensureToken();
      
      // 重置分页状态
      this.resetPagination();
      
      // 获取第一页留言
      this.fetchMessages(name);
    },
    
    resetPagination() {
      this.currentPage = 1;
      this.messages = [];
      this.hasMoreMessages = false;
      this.totalMessages = 0;
    },
    
    async ensureToken() {
      if (this.token) {
        return Promise.resolve();
      }
      
      const app = getApp();
      this.token = null;
      try {
        await app.ensureLogin();
        this.token = app.globalData.token;
        return true;
      } catch (e) {
        console.error('刷新 Token 失败:', e);
        uni.showToast({title:'登录已失效，请稍后重试',icon:'none'});
        return false;
      }
    },
    
    formatName(name) {
      if (!name || name.length < 2) return name;
      return name.charAt(0) + 'xx';
    },
    
    shareToFriend() {
    },

    async fetchMessages(name = '', isLoadMore = false) {
      // --- 新增前端校验 ---
      if (!name || name.trim() === '') {
          console.warn("fetchMessages 检测到空的搜索名称，已阻止请求。");
          // 可以根据需要决定是否提示用户或仅阻止请求
          // uni.showToast({ title: '搜索名称不能为空', icon: 'none' });
          this.isLoading = false; // 确保重置加载状态
          this.showSkeleton = false; // 隐藏骨架屏
          this.messages = []; // 清空消息
          this.totalMessages = 0;
          this.hasMoreMessages = false;
          return; // 直接返回，不发起请求
      }
      // --- 前端校验结束 ---

      try {
        if (this.isLoading) return; // 防止重复请求
        
        this.isLoading = true;
        // 重置动画状态
        this.showAnimation = false;
        
        // 如果不是加载更多，显示骨架屏
        if (!isLoadMore) {
          this.showSkeleton = true;
        }
        
        await this.ensureToken();
        
        const encodedName = encodeURIComponent(name);
        
        const res = await uni.request({
          url: `${this.$baseUrl}/mailbox/messages?query=${encodedName}&page=${this.currentPage}&pageSize=${this.pageSize}`,
          method: 'GET',
          header: {
            'Authorization': `Bearer ${this.token}`
          }
        });
        
        if (res.statusCode === 401) {
          const success = await this.handleTokenExpired();
          if (success) {
            this.isLoading = false;
            this.showSkeleton = false;
            return this.fetchMessages(name, isLoadMore);
          }
          this.isLoading = false;
          this.showSkeleton = false;
          return;
        }
        
        // 处理用户被禁用的情况
        if (res.statusCode === 403 && res.data.code === 'user_banned') {
          uni.showModal({
            title: '账号已禁用',
            content: res.data.error || '您的账号已被禁用，无法查看留言',
            showCancel: false
          });
          this.isLoading = false;
          this.showSkeleton = false;
          return;
        }
        
        if (res.statusCode === 200) {
          // 处理新的数据结构
          const responseData = res.data.data || [];
          const pagination = res.data.pagination || {};
          const recommendations = res.data.recommendations || null;

          // 更新总留言数和是否有更多数据
          this.totalMessages = pagination.total || 0;
          this.hasMoreMessages = pagination.hasMore || false;

          // 如果是加载更多，追加数据；否则替换数据
          if (isLoadMore) {
            this.messages = [...this.messages, ...responseData];
          } else {
            this.messages = responseData;
          }

          // 处理推荐数据
          if (recommendations && recommendations.data && recommendations.data.length > 0) {
            this.recommendations = recommendations.data;
            this.recommendationTitle = recommendations.title || '推荐留言';
            this.showRecommendations = true;
          } else {
            this.recommendations = [];
            this.showRecommendations = false;
            this.recommendationTitle = '';
          }

          // 延迟显示动画
          setTimeout(() => {
            this.showAnimation = true;
          }, 50);

          // 如果没有找到留言且没有推荐，且提供了名称，通过首字匹配获取同姓氏推荐
          if (this.messages.length === 0 && !this.showRecommendations && name && name.length > 0) {
            // 确保名称有值且长度大于0
            this.fetchRandomMessages(); // 这个方法已经修改为可以接收首字参数
          }
        } else if (res.statusCode === 400) {
            // 处理 400 Bad Request
            uni.showToast({
              title: res.data.error || '请求参数错误', // 显示后端返回的错误信息
              icon: 'none'
            });
        } else {
          uni.showToast({
            title: '加载留言失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取留言失败:', error);
        uni.showToast({
          title: '无法连接到服务器',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
        
        // 无论成功失败都隐藏骨架屏，但需要短暂延迟，提供更好的用户体验
        setTimeout(() => {
          this.showSkeleton = false;
        }, 300);
      }
    },

    // 加载更多留言
    loadMoreMessages() {
      if (!this.hasMoreMessages || this.isLoading) return;
      
      this.currentPage++;
      this.fetchMessages(this.searchName, true);
    },

    async fetchRandomMessages() {
      try {
        // 重置动画状态
        this.showAnimation = false;
        
        await this.ensureToken();
        
        // 判断是否传入首字参数（如果有搜索名称，则取第一个字作为首字参数）
        const firstChar = this.searchName ? this.searchName[0] : '';
        const firstCharParam = firstChar ? `&first_char=${encodeURIComponent(firstChar)}` : '';
        
        const res = await uni.request({
          url: `${this.$baseUrl}/mailbox/random-messages?${firstCharParam}`,
          method: 'GET',
          header: {
            'Authorization': `Bearer ${this.token}`
          }
        });
        
        if (res.statusCode === 401) {
          const success = await this.handleTokenExpired();
          if (success) {
            return this.fetchRandomMessages();
          }
          return;
        }
        
        // 处理用户被禁用的情况
        if (res.statusCode === 403 && res.data.code === 'user_banned') {
          uni.showModal({
            title: '账号已禁用',
            content: res.data.error || '您的账号已被禁用，无法查看留言',
            showCancel: false
          });
          return;
        }
        
        if (res.statusCode === 200 && res.data.success) {
          this.recommendedMessages = res.data.data;
          this.firstChar = res.data.first_char; // 保存返回的首字符，用于显示
          
          // 延迟显示动画
          setTimeout(() => {
            this.showAnimation = true;
          }, 50);
        } else {
        }
      } catch (error) {
        console.error('获取推荐留言失败:', error);
      }
    },

    viewMessages() {
      if (this.searchName.includes('匿名')) {
        uni.showToast({
          title: '输入包含违规关键词',
          icon: 'none'
        });
        return;
      }
      // --- 新增前端校验 ---
      if (!this.searchName || this.searchName.trim() === '') {
        uni.showToast({
          title: '请输入要搜索的名字',
          icon: 'none'
        });
        return; // 阻止后续执行
      }
      // --- 前端校验结束 ---

      // --- 输入验证：检查是否包含SQL通配符 --- 
      if (this.searchName.includes('%') || this.searchName.includes('_')) {
        uni.showToast({
          title: '搜索名称包含无效字符',
          icon: 'none'
        });
        return;
      }
      // --- 输入验证结束 ---
      
      if (!this.searchName) {
        uni.showToast({
          title: '名字不能为空',
          icon: 'none'
        });
        return;
      }
      
      // 重置分页状态
      this.resetPagination();
      
      // 显示骨架屏
      this.showSkeleton = true;
      
      // 获取第一页数据
      this.fetchMessages(this.searchName);
    },

    goToWrite() {
      uni.navigateTo({
        url: `/pages/write/write?name=${encodeURIComponent(this.searchName)}`
      });
    },

    formatTime(timestamp) {
      let date;
      
      // 检查输入类型，处理多种可能的格式
      if (timestamp instanceof Date) {
        // 已经是日期对象，直接使用
        date = timestamp;
      } else if (typeof timestamp === 'string') {
        // 字符串格式处理
        if (timestamp.includes('T')) {
          // ISO 格式 (yyyy-MM-ddTHH:mm:ss)，iOS 兼容
          date = new Date(timestamp);
        } else if (timestamp.includes('-') && timestamp.includes(':')) {
          // 格式如 "2025-04-16 03:34:33"，iOS 不兼容，需要转换
          // 转换为 iOS 兼容的格式 "yyyy-MM-ddTHH:mm:ss"
          const formattedTimestamp = timestamp.replace(' ', 'T');
          date = new Date(formattedTimestamp);
          
          // 如果日期仍然无效，使用手动解析方法
          if (isNaN(date.getTime())) {
            const parts = timestamp.split(/[- :]/);
            // 月份参数需要减 1，因为 JavaScript 中月份从 0 开始计数
            date = new Date(parts[0], parts[1] - 1, parts[2], parts[3], parts[4], parts[5]);
          }
        } else {
          // 尝试直接解析其他格式
          date = new Date(timestamp);
          
          // 如果解析失败，使用备用方法
          if (isNaN(date.getTime())) {
            console.warn('无法解析日期格式:', timestamp);
            // 返回原始字符串，避免显示 "Invalid Date"
            return timestamp;
          }
        }
      } else {
        // 数字时间戳或其他格式，尝试直接解析
        date = new Date(timestamp);
      }
      
      // 确保解析后的日期有效
      if (isNaN(date.getTime())) {
        console.warn('日期解析后无效:', timestamp);
        return timestamp; // 返回原始值
      }
      
      // 格式化日期输出
      const yyyy = date.getFullYear();
      const MM = String(date.getMonth() + 1).padStart(2, '0');
      const dd = String(date.getDate()).padStart(2, '0');
      const HH = String(date.getHours()).padStart(2, '0');
      const mm = String(date.getMinutes()).padStart(2, '0');
      
      return `${yyyy}-${MM}-${dd} ${HH}:${mm}`;
    },

    onButtonTouchStart() {
      this.isButtonHovered = true;
    },

    onButtonTouchEnd() {
      this.isButtonHovered = false;
    },

    onInputFocus() {
      this.isInputFocused = true;
    },

    onInputBlur() {
      this.isInputFocused = false;
    },

    // 获取分享配置
    fetchShareConfig(pageKey) {
      const nameParam = this.searchName ? `&name=${encodeURIComponent(this.searchName)}` : '';
      uni.request({
        url: `${this.$baseUrl}/mailbox/share-config?pageKey=${pageKey}${nameParam}`,
        method: 'GET',
        header: this.token ? { 'Authorization': `Bearer ${this.token}` } : {},
        success: (res) => {
          if (res.statusCode === 200 && res.data && res.data.success) {
            // 更新分享配置
            this.shareConfig = {
              title: res.data.title || this.shareConfig.title,
              imageUrl: res.data.imageUrl || this.shareConfig.imageUrl
            };
          } else {
            console.error(`分享配置请求返回不成功:`, res.data); // 保留错误日志
          }
        },
        fail: (err) => {
          console.error('获取分享配置失败:', err); // 保留错误日志
          // 失败时保留默认值
        }
      });
    },
    
    // 格式化分享标题，替换占位符
    formatShareTitle(title, name) {
      if (!title || !name) return title;
      // 替换标题中的 {name} 占位符
      return title.replace(/{name}/g, name);
    },

    // 添加点赞/取消点赞方法
    toggleLike() {
      // 如果没有消息数据或者正在加载，不执行操作
      if (!this.messageData || !this.messageData.id || this.isLikeLoading) {
        return;
      }
      
      // 设置点赞加载状态
      this.isLikeLoading = true;
      
      uni.request({
        url: `${this.$baseUrl}/mailbox/messages/${this.messageData.id}/toggle_like`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${this.token}`
        },
        success: (res) => {
          // 处理 token 过期
          if (res.statusCode === 401) {
            uni.showToast({
              title: '登录信息已过期，请返回首页重试',
              icon: 'none'
            });
            return;
          }
          
          // 成功处理点赞
          if (res.statusCode === 200 && res.data && res.data.success) {
            // 更新当前消息的点赞状态和数量
            this.messageData.is_liked_by_user = res.data.action === 'liked';
            this.messageData.like_count = res.data.like_count;
            
            // 添加前端日志
            console.log(`[点赞日志 - 主消息] 操作成功: 消息ID ${this.messageData.id}, 新状态: ${res.data.action}, 新点赞数: ${res.data.like_count}`);
            
            // 如果在当前查找的消息列表中，也更新对应的消息
            const messageIndex = this.foundMessages.findIndex(m => m.id === this.messageData.id);
            if (messageIndex >= 0) {
              this.foundMessages[messageIndex].is_liked_by_user = this.messageData.is_liked_by_user;
              this.foundMessages[messageIndex].like_count = this.messageData.like_count;
            }
            
            // 显示操作结果提示
            uni.showToast({
              title: res.data.action === 'liked' ? '点赞成功' : '已取消点赞',
              icon: 'none',
              duration: 1500
            });
          } else {
            // 处理错误
            uni.showToast({
              title: res.data && res.data.error ? res.data.error : '操作失败，请重试',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error('点赞操作失败', err);
          uni.showToast({
            title: '网络错误，请稍后重试',
            icon: 'none'
          });
        },
        complete: () => {
          this.isLikeLoading = false;
        }
      });
    },
    
    // 添加用于留言列表的点赞方法
    toggleLikeMessage(message) {
      // --- 临时修改：禁止取消点赞 --- 
      // 如果消息已经被点赞，则直接返回，不允许取消
      if (message.is_liked_by_user) {
        // console.log('临时：禁止取消点赞'); // 可以取消注释来调试
        uni.showToast({ title: '你已经点过赞啦', icon: 'none' });
        return;
      }
      // --- 临时修改结束 ---
      
      // --- 频率限制检查 ---
      const now = Date.now();
      // 移除超出时间窗口的时间戳
      this.likeActionTimestamps = this.likeActionTimestamps.filter(
        timestamp => now - timestamp < this.RATE_LIMIT_WINDOW
      );
      // 检查是否达到限制
      if (this.likeActionTimestamps.length >= this.RATE_LIMIT_COUNT) {
        uni.showToast({
          title: '操作太快了，请稍后再试',
          icon: 'none'
        });
        return; // 阻止操作
      }
      // --- 频率限制检查结束 ---
      
      // 防止已经在处理中的点赞请求
      if (message._likeLoading) {
        return;
      }
      
      // 记录本次操作时间戳
      this.likeActionTimestamps.push(now);
      
      // 设置加载状态
      this.$set(message, '_likeLoading', true);
      
      // 确保有 token
      if (!this.token) {
          this.ensureToken().then(() => {
              if (this.token) {
                  this.requestLikeToggle(message); // 有token后发起请求
              } else {
                  uni.showToast({ title: '获取登录状态失败，请稍后重试', icon: 'none'});
                  this.$set(message, '_likeLoading', false);
              }
          }).catch(err => {
              uni.showToast({ title: '获取登录状态失败，请稍后重试', icon: 'none'});
              this.$set(message, '_likeLoading', false);
          });
          return; // 等待 ensureToken 完成
      }
      
      // 如果已有 token，直接发起请求
      this.requestLikeToggle(message);
    },

    // 拆分出实际的请求逻辑
    async requestLikeToggle(message) {
      try {
          const res = await uni.request({
            url: `${this.$baseUrl}/mailbox/messages/${message.id}/toggle_like`,
            method: 'POST',
            header: {
              'Authorization': `Bearer ${this.token}`
            }
          });
          
          // 处理 token 过期
          if (res.statusCode === 401) {
            const success = await this.handleTokenExpired();
            if (success) {
              // 移除加载状态
              this.$set(message, '_likeLoading', false);
              // Token刷新成功，重新尝试点赞
              this.toggleLikeMessage(message);
            } else {
              uni.showToast({
                title: '登录信息已过期，请返回首页重试',
                icon: 'none'
              });
              // Token刷新失败，移除加载状态
              this.$set(message, '_likeLoading', false);
            }
            return; // 明确返回
          }
          
          // 成功处理点赞
          if (res.statusCode === 200 && res.data && res.data.success) {
            // 更新消息的点赞状态和数量
            message.is_liked_by_user = res.data.action === 'liked';
            message.like_count = res.data.like_count;
            
            // 添加前端日志
            console.log(`[点赞日志 - 列表项] 操作成功: 消息ID ${message.id}, 新状态: ${res.data.action}, 新点赞数: ${res.data.like_count}`);
            
          } else {
            // 处理错误
            uni.showToast({
              title: res.data && res.data.error ? res.data.error : '操作失败，请重试',
              icon: 'none'
            });
          }
      } catch (err) {
          console.error('点赞操作失败', err);
          uni.showToast({
            title: '网络错误，请稍后重试',
            icon: 'none'
          });
      } finally {
          // 使用 $nextTick 确保在DOM更新后移除加载状态
          this.$nextTick(() => {
             this.$set(message, '_likeLoading', false);
          });
      }
    },

    formatAvatarUrl(avatarUrl) {
      if (!avatarUrl) {
        return '/static/view-avatar.png';
      }
      if (avatarUrl.startsWith('http')) {
        return avatarUrl;
      }
      // 确保 baseUrl 和 avatarUrl 之间只有一个斜杠
      const baseUrl = this.$baseUrl.endsWith('/') ? this.$baseUrl.slice(0, -1) : this.$baseUrl;
      const avatarPath = avatarUrl.startsWith('/') ? avatarUrl : `/${avatarUrl}`;
      
      return baseUrl + avatarPath;
    },
  }
};
</script>

<style>
/* 基础容器样式 */
.container {
  min-height: 100vh;
  height: 100%; 
  background: linear-gradient(180deg, #fdfbfb 0%, #f6f7fb 100%);
  background-attachment: fixed; 
  position: relative;
  overflow-x: hidden; 
  overflow-y: auto;
  /* 当前设备状态栏（刘海 / 导航栏）高度 */
  /* padding-top: var(--status-bar-height); */
  padding-top: 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 背景效果 */
.gradient-blob {
  position: fixed;
  border-radius: 50%;
  opacity: 0.3;
  filter: blur(80rpx);
  z-index: 0;
  will-change: transform, opacity, border-radius;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.blob-1 {
  width: 500rpx;
  height: 450rpx;
  background: radial-gradient(circle, rgba(255, 111, 145, 0.5) 0%, rgba(254, 172, 94, 0.3) 100%);
  top: -150rpx;
  left: -100rpx;
  animation: blobMorph 20s cubic-bezier(0.45, 0.05, 0.55, 0.95) infinite alternate;
}

.blob-2 {
  width: 600rpx;
  height: 550rpx;
  background: radial-gradient(circle, rgba(79, 95, 232, 0.4) 0%, rgba(168, 209, 242, 0.2) 100%);
  bottom: -200rpx;
  right: -150rpx;
  animation: blobMorph 25s cubic-bezier(0.37, 0, 0.63, 1) infinite alternate-reverse 2s;
}

.blob-3 {
  width: 400rpx;
  height: 380rpx;
  background: radial-gradient(circle, rgba(132, 250, 176, 0.4) 0%, rgba(143, 211, 244, 0.2) 100%);
  top: 30%;
  right: 5%;
  animation: blobMorph 18s cubic-bezier(0.65, 0.05, 0.36, 1) infinite alternate 1s;
}

@keyframes blobMorph {
  0%, 100% {
    transform: translate3d(0, 0, 0) scale(1) rotate(0deg);
    border-radius: 50% 45% 55% 48%;
    opacity: 0.3;
  }
  25% {
    transform: translate3d(80rpx, -100rpx, 0) scale(1.1) rotate(45deg);
    border-radius: 45% 55% 48% 50%;
    opacity: 0.4;
  }
  50% {
    transform: translate3d(-60rpx, 90rpx, 0) scale(0.9) rotate(-30deg);
    border-radius: 55% 48% 50% 45%;
    opacity: 0.25;
  }
  75% {
    transform: translate3d(90rpx, 120rpx, 0) scale(1.05) rotate(90deg);
    border-radius: 48% 50% 45% 55%;
    opacity: 0.35;
  }
}

.decorative-pattern {
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  background-image: radial-gradient(rgba(0, 0, 0, 0.08) 0.5px, transparent 0.5px);
  background-size: 15rpx 15rpx;
  opacity: 0.5;
  z-index: 0;
  animation: patternPulse 15s ease-in-out infinite;
}

@keyframes patternPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.15; }
}

/* 内容区域 */
.content {
  padding: 20rpx 30rpx;
  z-index: 1;
  position: relative;
  flex: 1;
  width: 100%;
  box-sizing: border-box;
}

/* 头部样式 */
.header {
  margin-bottom: 30rpx;
  text-align: center;
  background: linear-gradient(135deg, rgba(255, 112, 154, 0.7) 0%, rgba(254, 225, 64, 0.7) 100%);
  padding: 40rpx 0;
  border-radius: 24rpx;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.4);
  z-index: -1;
}

.title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  text-shadow: 0 2rpx 3rpx rgba(0, 0, 0, 0.1);
  position: relative;
  display: inline-block;
}

.title::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -10rpx;
  width: 40rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #ffa9c6, #a5c7fe);
  border-radius: 3rpx;
  transform: translateX(-50%);
}

.subtitle {
  font-size: 24rpx;
  color: rgba(51, 51, 51, 0.8);
  margin-top: 20rpx;
  display: block;
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  margin-bottom: 30rpx;
}

.search-input-wrapper {
  flex: 1;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 30rpx;
  margin-right: 20rpx;
  padding: 4rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
}

.search-input {
  height: 80rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
  color: #333;
}

.search-button {
  width: 160rpx;
  height: 80rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #ffa9c6, #a5c7fe);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.3s ease;
  box-shadow: 0 6rpx 15rpx rgba(255, 169, 198, 0.25);
  border: none;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  background-size: cover;
  margin-right: 8rpx;
}

.search-button:active {
  transform: scale(0.96);
  box-shadow: 0 3rpx 8rpx rgba(79, 95, 232, 0.15);
}

.search-button .button-text {
  font-weight: 600;
  font-size: 28rpx;
}

/* 恢复原来的气泡样式 */
.message-bubble {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0 20px 0;
  animation: fadeIn 0.5s ease;
  width: 100%;
}

.bubble-text {
  font-size: 14px;
  color: #ff9a9e;
  font-weight: 500;
}

.invite-mini-btn {
  background: linear-gradient(to right, #ff7eb3, #ff758c);
  border: none;
  border-radius: 20px;
  padding: 4px 16px;
  height: auto;
  min-height: unset;
  line-height: normal;
  font-size: 12px;
  color: #fff;
  margin: 0;
}

.invite-mini-btn:active {
  opacity: 0.9;
}

/* 留言列表 */
.message-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 18rpx;
  padding: 24rpx 28rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06), 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
  transition: all 0.2s ease;
  overflow: hidden;
}

.message-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.04);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.user-avatar-wrapper {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 45rpx;
  height: 45rpx;
  border-radius: 50%;
  margin-right: 8rpx;
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.message-user {
  font-size: 24rpx;
  color: #ff758f;
  font-weight: 600;
  position: relative;
  background: rgba(255, 240, 245, 0.5);
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
}

.message-user::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2rpx;
  width: 100%;
  height: 1rpx;
  background: currentColor;
  opacity: 0.3;
}

.recipient-wrapper {
  display: flex;
  align-items: center;
  background: rgba(255, 247, 247, 0.5);
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.recipient-wrapper .user-avatar {
  margin-right: 8rpx;
  flex-shrink: 0;
}

.recipient-prefix, .recipient-suffix {
  font-size: 22rpx;
  color: #999;
}

.recipient-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #ff758c;
  margin: 0 4rpx;
  position: relative;
}

.recipient-name::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2rpx;
  width: 100%;
  height: 1rpx;
  background: currentColor;
  opacity: 0.3;
}

/* 右侧元数据区域 - 点赞和时间 */
.message-meta {
  display: flex;
  align-items: center;
}

/* 点赞按钮新设计 - 调整大小 */
.message-like-btn {
  display: flex;
  align-items: center;
  padding: 6rpx 10rpx; /* 减小内边距 */
  border-radius: 40rpx;
  background-color: rgba(249, 249, 249, 0.8);
  position: relative;
  transition: all 0.2s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.03);
}

.message-like-btn:active {
  transform: scale(0.92);
}

.message-like-btn.liked {
  background-color: rgba(255, 240, 245, 0.9);
}

.message-like-btn.liked .like-icon {
  background-image: url('data:image/svg+xml;utf8,<svg t="1713265341761" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M512 936.1c-9.1 0-18.2-3.2-25.5-9.6-7.4-6.4-181.5-159.6-289.8-280.1-76.7-85.5-115.5-160.9-115.5-224.5 0-140.4 114.3-254.7 254.7-254.7 65.1 0 126.7 24.8 173.2 69.9 46.5-45.1 108.1-69.9 173.2-69.9 140.4 0 254.7 114.3 254.7 254.7 0 63.6-38.9 139-115.5 224.5-108.3 120.6-282.4 273.7-289.8 280.1-7.4 6.4-16.5 9.6-25.5 9.6z" fill="%23FF5A5F" p-id="2878"></path></svg>');
  transform: scale(1.1); /* 减小放大效果 */
}

.message-like-btn.liked .like-count {
  color: #ff5a5f;
  font-weight: 600;
}

.like-icon {
  width: 28rpx; /* 减小图标大小 */
  height: 28rpx; /* 减小图标大小 */
  background-image: url('data:image/svg+xml;utf8,<svg t="1713265341761" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M512 936.1c-9.1 0-18.2-3.2-25.5-9.6-7.4-6.4-181.5-159.6-289.8-280.1-76.7-85.5-115.5-160.9-115.5-224.5 0-140.4 114.3-254.7 254.7-254.7 65.1 0 126.7 24.8 173.2 69.9 46.5-45.1 108.1-69.9 173.2-69.9 140.4 0 254.7 114.3 254.7 254.7 0 63.6-38.9 139-115.5 224.5-108.3 120.6-282.4 273.7-289.8 280.1-7.4 6.4-16.5 9.6-25.5 9.6z" fill="%23CCCCCC" p-id="2878"></path></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.like-count {
  font-size: 22rpx; /* 减小字体大小 */
  color: #999;
  margin-left: 4rpx; /* 减小左侧间距 */
  min-width: 22rpx; /* 调整最小宽度 */
  text-align: center;
  transition: all 0.2s ease;
}

/* 时间显示改进 */
.message-time {
  font-size: 22rpx;
  color: #aaa;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  background: rgba(0, 0, 0, 0.03);
  margin-right: 12rpx; /* 添加右侧间距 */
}

/* 内容区域改进 */
.message-content-wrapper {
  padding-left: 12rpx;
  position: relative;
}

.message-content {
  font-size: 28rpx;
  color: #444;
  line-height: 1.6;
  word-break: break-all;
}

/* 移除不再需要的样式 */
.message-actions,
.message-like-area {
  display: none;
}

/* 消息动画效果优化 */
@keyframes messageAppear {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-appear {
  animation: messageAppear 0.5s ease forwards;
}

/* 骨架屏样式 */
.skeleton-container {
  margin: 20rpx 0;
}

/* 新增：骨架屏闪烁效果基类 */
.skeleton-shimmer-base {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.skeleton-item {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  position: relative;
}

.skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.skeleton-user {
  width: 120rpx;
  height: 28rpx;
  border-radius: 6rpx;
}

.skeleton-time {
  width: 160rpx;
  height: 22rpx;
  border-radius: 12rpx;
}

.skeleton-content {
  width: 100%;
  height: 28rpx;
  border-radius: 6rpx;
  margin-bottom: 12rpx;
}

.skeleton-content.short {
  width: 60%;
}

/* 添加shimmer动画代替原来的skeletonPulse */
@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 底部提示样式 */
.bottom-hint {
  text-align: center;
  margin: 30rpx 0;
}

.hint-link {
  font-size: 28rpx;
  color: #ff758f;
  display: inline-block;
  padding: 12rpx 30rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 117, 143, 0.1);
  transition: all 0.3s ease;
}

.hint-link:active {
  transform: scale(0.96);
  box-shadow: 0 2rpx 4rpx rgba(255, 117, 143, 0.1);
}

/* 无数据引导样式 */
.no-data-guide {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  animation: fadeIn 0.5s ease;
}

.guide-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
}

.icon {
  width: 100%;
  height: 100%;
}

.guide-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  text-align: center;
}

.highlight {
  color: #ff758f;
  font-weight: 700;
  position: relative;
  display: inline-block;
}

.highlight::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2rpx;
  width: 100%;
  height: 2rpx;
  background: currentColor;
  opacity: 0.3;
}

.guide-subtext {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 30rpx;
  text-align: center;
}

/* 按钮组 - 恢复原来的垂直布局 */
.button-group {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 15px;
  padding: 0 20px;
}

.write-button, .invite-button {
  width: 100%;
  height: 45px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.3s ease;
}

.write-button {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  box-shadow: 0 4px 15px rgba(250, 112, 154, 0.2);
}

.invite-button {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  box-shadow: 0 4px 15px rgba(67, 233, 123, 0.2);
}

.write-button:active, .invite-button:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.button-text {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* 推荐留言区域 */
.recommended-messages {
  margin-top: 30rpx;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  animation: fadeIn 0.5s ease;
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
}

.section-subtitle {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 20rpx;
  display: block;
  font-style: italic;
}

.recommended-item::before {
  display: none;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .header {
    padding: 20px 0;
  }

  .title {
    font-size: 20px;
  }
  
  .bubble-text {
    font-size: 13px;
  }
  
  .hint-link {
    font-size: 13px;
  }

  .search-input, .search-button {
    height: 45px;
    font-size: 14px;
  }

  .guide-text {
    font-size: 16px;
  }

  .message-content {
    font-size: 14px;
  }

  .message-user, .message-time {
    font-size: 10px;
  }

  .write-button, .invite-button {
    height: 40px;
  }

  .button-text {
    font-size: 14px;
  }
  
  .section-title {
    font-size: 15px;
  }
  
  .section-subtitle {
    font-size: 12px;
  }
}

/* 留言详情卡片样式 */
.message-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 20rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.06);
  position: relative;
  min-height: 300rpx;
  display: flex;
  flex-direction: column;
}

/* 添加点赞区域样式 */
.message-actions {
  display: flex;
  justify-content: flex-end; /* 右对齐 */
  margin-top: 16rpx;
  width: 100%;
}

/* 优化点赞区域样式 */
.message-like-area {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 40rpx;
  background-color: rgba(249, 249, 249, 0.8);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
  transition: all 0.2s ease;
}

.message-like-area:active {
  transform: scale(0.95);
  background-color: rgba(255, 240, 245, 0.9);
}

.like-icon {
  width: 36rpx;
  height: 36rpx;
  background-image: url('data:image/svg+xml;utf8,<svg t="1713265341761" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M512 936.1c-9.1 0-18.2-3.2-25.5-9.6-7.4-6.4-181.5-159.6-289.8-280.1-76.7-85.5-115.5-160.9-115.5-224.5 0-140.4 114.3-254.7 254.7-254.7 65.1 0 126.7 24.8 173.2 69.9 46.5-45.1 108.1-69.9 173.2-69.9 140.4 0 254.7 114.3 254.7 254.7 0 63.6-38.9 139-115.5 224.5-108.3 120.6-282.4 273.7-289.8 280.1-7.4 6.4-16.5 9.6-25.5 9.6z" fill="%23CCCCCC" p-id="2878"></path></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.like-icon.liked {
  background-image: url('data:image/svg+xml;utf8,<svg t="1713265341761" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M512 936.1c-9.1 0-18.2-3.2-25.5-9.6-7.4-6.4-181.5-159.6-289.8-280.1-76.7-85.5-115.5-160.9-115.5-224.5 0-140.4 114.3-254.7 254.7-254.7 65.1 0 126.7 24.8 173.2 69.9 46.5-45.1 108.1-69.9 173.2-69.9 140.4 0 254.7 114.3 254.7 254.7 0 63.6-38.9 139-115.5 224.5-108.3 120.6-282.4 273.7-289.8 280.1-7.4 6.4-16.5 9.6-25.5 9.6z" fill="%23FF5A5F" p-id="2878"></path></svg>');
  transform: scale(1.2);
}

.like-count {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
  min-width: 24rpx;
  text-align: center;
  font-weight: 500;
}
</style>
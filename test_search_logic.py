#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试留言检索逻辑的修改效果
"""

import requests
import json

# 测试配置
BASE_URL = "http://localhost:5000"  # 根据实际部署地址调整
TEST_TOKEN = "your_test_token_here"  # 需要替换为有效的JWT <PERSON>

def test_search_logic():
    """测试搜索逻辑"""
    headers = {
        'Authorization': f'Bearer {TEST_TOKEN}',
        'Content-Type': 'application/json'
    }
    
    test_cases = [
        {
            'name': '单字符搜索测试',
            'query': '张',
            'expected': '只返回收信人姓名确实是"张"的留言'
        },
        {
            'name': '多字符搜索有结果测试',
            'query': '张三',
            'expected': '返回包含"张三"的留言'
        },
        {
            'name': '多字符搜索无结果测试',
            'query': '张不存在的人',
            'expected': '返回同姓推荐'
        }
    ]
    
    for case in test_cases:
        print(f"\n=== {case['name']} ===")
        print(f"搜索关键词: {case['query']}")
        print(f"预期结果: {case['expected']}")
        
        # 发送请求
        url = f"{BASE_URL}/mailbox/messages"
        params = {
            'query': case['query'],
            'page': 1,
            'pageSize': 5
        }
        
        try:
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                print(f"响应状态: 成功")
                print(f"找到留言数量: {len(data.get('data', []))}")
                
                # 检查是否有同姓推荐
                if 'surname_recommendations' in data:
                    recommendations = data['surname_recommendations']
                    print(f"同姓推荐: {recommendations['message']}")
                    print(f"推荐数量: {len(recommendations['data'])}")
                
                # 显示前几条留言的收信人姓名
                messages = data.get('data', [])
                if messages:
                    print("留言收信人姓名:")
                    for i, msg in enumerate(messages[:3]):
                        print(f"  {i+1}. {msg.get('name', 'N/A')}")
                
            else:
                print(f"响应状态: 失败 ({response.status_code})")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"请求失败: {str(e)}")

def print_modification_summary():
    """打印修改总结"""
    print("=" * 60)
    print("留言检索逻辑修改总结")
    print("=" * 60)
    print()
    print("主要修改内容:")
    print("1. 单字符搜索逻辑:")
    print("   - 修改前: 搜索'张'返回所有姓张的留言（张三、张四等）")
    print("   - 修改后: 搜索'张'只返回收信人姓名确实是'张'的留言")
    print()
    print("2. 多字符搜索逻辑:")
    print("   - 保持原有的完全匹配和包含匹配功能")
    print("   - 当搜索无结果时，提供同姓推荐功能")
    print()
    print("3. 技术实现:")
    print("   - 单字符: WHERE am.name = %s (精确匹配)")
    print("   - 多字符: WHERE (am.name = %s OR am.name LIKE %s) (完全匹配或包含匹配)")
    print("   - 同姓推荐: WHERE am.name LIKE '姓氏%' (仅在无结果时触发)")
    print()
    print("4. API响应格式:")
    print("   - 保持原有响应结构不变")
    print("   - 新增 surname_recommendations 字段（仅在有推荐时返回）")
    print()

if __name__ == "__main__":
    print_modification_summary()
    
    # 如果有有效的Token，可以运行测试
    if TEST_TOKEN != "your_test_token_here":
        test_search_logic()
    else:
        print("提示: 请设置有效的TEST_TOKEN来运行实际测试")
